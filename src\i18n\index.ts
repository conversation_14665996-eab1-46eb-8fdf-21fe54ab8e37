import i18n from 'i18next'
import { initReactI18next } from 'react-i18next'

import en from './locales/en.json'
import zh_CN from './locales/zh_CN.json'
import zh_TW from './locales/zh_TW.json'
import hi from './locales/hi.json'
import es from './locales/es.json'
import fr from './locales/fr.json'
import ru from './locales/ru.json'
import id from './locales/id.json'
import bn from './locales/bn.json'
import pt from './locales/pt.json'
import de from './locales/de.json'
import ja from './locales/ja.json'
import ko from './locales/ko.json'
import vi from './locales/vi.json'
import tr from './locales/tr.json'
import it from './locales/it.json'

// 语言映射表
const languageMap: Record<string, any> = {
  'en': en,
  'zh': zh_CN,
  'zh-CN': zh_CN,
  'zh_CN': zh_CN,
  'zh-Hans': zh_CN,
  'zh-TW': zh_TW,
  'zh_TW': zh_TW,
  'zh-Hant': zh_TW,
  'hi': hi,
  'es': es,
  'fr': fr,
  'ru': ru,
  'id': id,
  'bn': bn,
  'pt': pt,
  'de': de,
  'ja': ja,
  'ko': ko,
  'vi': vi,
  'tr': tr,
  'it': it
}

// 为 background script 提供的简单多语言函数
export function getBackgroundText(key: string): string {
  const currentLanguage = chrome.i18n.getUILanguage()

  // 尝试精确匹配
  let translations = languageMap[currentLanguage]

  // 如果没有精确匹配，尝试语言前缀匹配
  if (!translations) {
    const languagePrefix = currentLanguage.split('-')[0]
    translations = languageMap[languagePrefix]
  }

  // 如果还是没有，回退到英语
  if (!translations) {
    translations = en
  }

  // 支持嵌套键，如 'common.title'
  const keys = key.split('.')
  let result: any = translations

  for (const k of keys) {
    result = result?.[k]
    if (result === undefined) break
  }

  return result || key
}

const resources = {
  en: { translation: en },
  zh_CN: { translation: zh_CN },
  zh_TW: { translation: zh_TW },
  hi: { translation: hi },
  es: { translation: es },
  fr: { translation: fr },
  ru: { translation: ru },
  id: { translation: id },
  bn: { translation: bn },
  pt: { translation: pt },
  de: { translation: de },
  ja: { translation: ja },
  ko: { translation: ko },
  vi: { translation: vi },
  tr: { translation: tr },
  it: { translation: it }
}

// 检测浏览器语言
async function detectLanguage(): Promise<string> {
  try {
    // 首先检查是否有用户保存的语言偏好
    const stored = await chrome.storage.local.get(['snapany_user_language'])
    if (stored.snapany_user_language && resources[stored.snapany_user_language]) {
      return stored.snapany_user_language
    }

    const chromeLanguage = chrome?.i18n?.getUILanguage?.()
    if (chromeLanguage) {
      // 标准化语言代码
      const normalizedLang = chromeLanguage.toLowerCase().replace('-', '_')

      // 检查是否支持该语言
      if (resources[normalizedLang]) {
        return normalizedLang
      }

      // 尝试语言前缀匹配
      const languagePrefix = normalizedLang.split('_')[0]
      if (languagePrefix === 'zh') {
        // 根据地区代码决定使用简体还是繁体中文
        if (normalizedLang.includes('tw') || normalizedLang.includes('hk') || normalizedLang.includes('mo') || normalizedLang.includes('hant')) {
          return 'zh_TW'
        }
        return 'zh_CN' // 默认使用简体中文
      }
      if (resources[languagePrefix]) {
        return languagePrefix
      }
    }
  } catch (error) {
    console.log('Chrome i18n API不可用，使用navigator.language')
  }

  const browserLanguage = navigator.language || navigator.languages?.[0] || 'en'
  const normalizedBrowserLang = browserLanguage.toLowerCase().replace('-', '_')

  // 检查是否支持该语言
  if (resources[normalizedBrowserLang]) {
    return normalizedBrowserLang
  }

  // 尝试语言前缀匹配
  const languagePrefix = normalizedBrowserLang.split('_')[0]
  if (languagePrefix === 'zh') {
    // 根据地区代码决定使用简体还是繁体中文
    if (normalizedBrowserLang.includes('tw') || normalizedBrowserLang.includes('hk') || normalizedBrowserLang.includes('mo') || normalizedBrowserLang.includes('hant')) {
      return 'zh_TW'
    }
    return 'zh_CN' // 默认使用简体中文
  }
  if (resources[languagePrefix]) {
    return languagePrefix
  }

  // 回退到英语
  return 'en'
}

// 异步初始化i18n
async function initializeI18n() {
  const detectedLanguage = await detectLanguage()

  await i18n
    .use(initReactI18next)
    .init({
      resources,
      lng: detectedLanguage,
      fallbackLng: 'en',
      debug: process.env.NODE_ENV === 'development',

      interpolation: {
        escapeValue: false
      }
    })
}

// 立即初始化
initializeI18n().catch(console.error)

export default i18n