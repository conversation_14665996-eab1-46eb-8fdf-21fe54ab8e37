/**
 * CORS绕过服务
 * 使用declarativeNetRequest API来绕过下载页面的CORS验证
 */

export class CorsManager {
  private isEnabled: boolean = false
  private ruleId: number = 100001 // 用于CORS规则的ID

  constructor() {
    this.setupCorsRules()
  }

  /**
   * 启用CORS绕过功能
   */
  public async enable(): Promise<void> {
    this.isEnabled = true
    await this.addCorsRules()
    console.log("CORS绕过功能已启用")
  }

  /**
   * 禁用CORS绕过功能
   */
  public async disable(): Promise<void> {
    this.isEnabled = false
    await this.removeCorsRules()
    console.log("CORS绕过功能已禁用")
  }

  /**
   * 设置CORS规则（初始化时调用）
   */
  private setupCorsRules(): void {
    // 初始化时不添加规则，等待enable()调用
    console.log("CORS管理器已初始化")
  }

  /**
   * 添加CORS绕过规则
   */
  private async addCorsRules(): Promise<void> {
    try {
      // 定义CORS绕过规则
      const rules: chrome.declarativeNetRequest.Rule[] = [
        {
          id: this.ruleId,
          priority: 1,
          action: {
            type: chrome.declarativeNetRequest.RuleActionType.MODIFY_HEADERS,
            responseHeaders: [
              {
                header: "Access-Control-Allow-Origin",
                operation: chrome.declarativeNetRequest.HeaderOperation.SET,
                value: "*"
              },
              {
                header: "Access-Control-Allow-Methods",
                operation: chrome.declarativeNetRequest.HeaderOperation.SET,
                value: "GET, POST, PUT, DELETE, OPTIONS, HEAD"
              },
              {
                header: "Access-Control-Allow-Headers",
                operation: chrome.declarativeNetRequest.HeaderOperation.SET,
                value: "*"
              },
              {
                header: "Access-Control-Allow-Credentials",
                operation: chrome.declarativeNetRequest.HeaderOperation.SET,
                value: "true"
              }
            ]
          },
          condition: {
            initiatorDomains: ["localhost", "snapany.com"],
            resourceTypes: [
              chrome.declarativeNetRequest.ResourceType.XMLHTTPREQUEST,
              chrome.declarativeNetRequest.ResourceType.OTHER
            ]
          }
        }
      ]

      // 添加规则
      await chrome.declarativeNetRequest.updateDynamicRules({
        addRules: rules
      })

      console.log("CORS绕过规则已添加")
    } catch (error) {
      console.error("添加CORS规则失败:", error)
    }
  }

  /**
   * 移除CORS绕过规则
   */
  private async removeCorsRules(): Promise<void> {
    try {
      await chrome.declarativeNetRequest.updateDynamicRules({
        removeRuleIds: [this.ruleId]
      })
      console.log("CORS绕过规则已移除")
    } catch (error) {
      console.error("移除CORS规则失败:", error)
    }
  }



  /**
   * 获取当前状态
   */
  public getStatus(): { enabled: boolean } {
    return {
      enabled: this.isEnabled
    }
  }
}
