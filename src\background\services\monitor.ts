// 网络请求监控模块
// 负责监听、合并和处理网络请求信息

import type { RequestInfo, RequestStats, FilteredRequestInfo } from "@/types/network"
import { requestFilter } from "./filter"
import { extractDomain, getTabInfo } from "@/utils/tabUtils"

/**
 * 网络请求监控器类
 * 负责监听网络请求，合并请求和响应信息，并提供统计功能
 */
export class RequestMonitor {
  // 请求监控状态
  private isMonitoringEnabled = true

  // 更新回调函数
  private onUpdateCallback?: () => void

  // 请求统计信息
  private requestStats: RequestStats = {
    totalRequests: 0,
    requestsByType: {},
    requestsByDomain: {},
    lastRequestTime: 0
  }



  // 存储通过过滤的请求信息，按标签页隔离（最多每个标签页保存200条）
  private filteredRequestsByTab = new Map<number, FilteredRequestInfo[]>()
  private readonly MAX_FILTERED_REQUESTS = 200

  // 存储正在进行的请求（用于合并请求信息）
  private pendingRequests = new Map<string, RequestInfo>()

  constructor() {
    this.setupRequestListeners() // 监听网络请求开始 - 创建请求记录
    this.setupTabListeners() // 监听标签页关闭，清理对应的请求数据
    this.updateBadge() // 初始化角标
    console.log("🔍 网络请求监控器已初始化")
  }

  /**
   * 处理完成的请求
   */
  private async processCompletedRequest(
    requestInfo: RequestInfo,
    details: chrome.webRequest.WebRequestDetails,
    duration: number
  ) {
    const { title: pageTitle, url: pageUrl, favIconUrl } = await getTabInfo(details.tabId)

    // 打印完整的请求信息
    this.printCompleteRequestInfo(requestInfo, 'completed', undefined, pageTitle, pageUrl, favIconUrl, duration)
  }

  /**
   * 打印完整的请求信息
   */
  private printCompleteRequestInfo(
    requestInfo: RequestInfo,
    status: 'completed' | 'failed',
    error?: string,
    pageTitle?: string,
    pageUrl?: string,
    favIconUrl?: string,
    duration?: number
  ) {
    const domain = extractDomain(requestInfo.url)
    const timestamp = Date.now()

    // 构建详细信息对象
    const details: RequestInfo = {
      ...requestInfo,
      pageTitle,
      pageUrl,
      favIconUrl
    }

    // 对details进行过滤，应用过滤规则
    const filteredDetails = requestFilter.shouldShowRequest(details)
    if (filteredDetails) {
      // console.log('通过过滤:', details)

      // 将通过过滤的请求添加到过滤请求列表
      const filteredRequestInfo: FilteredRequestInfo = {
        ...requestInfo,
        domain,
        timestamp,
        duration: duration || 0,
        error,
        status,
        pageTitle,
        pageUrl,
        favIconUrl
      }
      this.addToFilteredRequests(filteredRequestInfo)
    }
  }

  /**
   * 更新请求统计
   */
  private updateRequestStats(details: chrome.webRequest.WebRequestDetails) {
    if (!this.isMonitoringEnabled) return

    this.requestStats.totalRequests++
    this.requestStats.lastRequestTime = details.timeStamp

    // 按类型统计
    const type = details.type || 'unknown'
    this.requestStats.requestsByType[type] = (this.requestStats.requestsByType[type] || 0) + 1

    // 按域名统计
    const domain = extractDomain(details.url)
    this.requestStats.requestsByDomain[domain] = (this.requestStats.requestsByDomain[domain] || 0) + 1
  }



  /**
   * 添加请求到过滤请求列表（带去重功能，按标签页隔离）
   */
  private addToFilteredRequests(requestInfo: FilteredRequestInfo) {
    const tabId = requestInfo.tabId

    // 获取或创建该标签页的请求列表
    if (!this.filteredRequestsByTab.has(tabId)) {
      this.filteredRequestsByTab.set(tabId, [])
    }

    const tabRequests = this.filteredRequestsByTab.get(tabId)!

    // 检查是否已存在相同URL的请求，如果存在则直接返回
    const existingRequest = tabRequests.find(
      (existing: FilteredRequestInfo) => existing.url === requestInfo.url
    )

    if (existingRequest) {
      return
    }

    // 添加新记录到最前面
    tabRequests.unshift(requestInfo)
    console.log(`➕ 添加新请求 (标签页 ${tabId}):`, requestInfo.url)

    // 限制每个标签页的请求数量
    if (tabRequests.length > this.MAX_FILTERED_REQUESTS) {
      tabRequests.pop()
    }

    // 更新角标
    this.updateBadge()

    // 通知更新
    if (this.onUpdateCallback) {
      this.onUpdateCallback()
    }
  }

  /**
   * 获取所有过滤请求（合并所有标签页）
   */
  private getAllFilteredRequests(): FilteredRequestInfo[] {
    const allRequests: FilteredRequestInfo[] = []

    // 合并所有标签页的请求
    for (const requests of this.filteredRequestsByTab.values()) {
      allRequests.push(...requests)
    }

    // 按时间戳排序（最新的在前）
    allRequests.sort((a, b) => b.timestamp - a.timestamp)

    return allRequests
  }

  /**
   * 设置标签页监听器
   */
  private setupTabListeners() {
    // 监听标签页关闭，清理对应的请求数据
    chrome.tabs.onRemoved.addListener((tabId) => {
      if (this.filteredRequestsByTab.has(tabId)) {
        this.filteredRequestsByTab.delete(tabId)
        console.log(`🗑️ 已清理标签页 ${tabId} 的请求数据`)

        // 清理相关的 Chrome Storage 数据
        this.cleanupStorageForTab(tabId)

        // 更新角标
        this.updateBadge()

        // 通知更新
        if (this.onUpdateCallback) {
          this.onUpdateCallback()
        }
      }
    })

    // 监听标签页激活，更新角标显示
    chrome.tabs.onActivated.addListener((activeInfo) => {
      console.log(`🔄 标签页切换到: ${activeInfo.tabId}`)

      // 直接使用切换到的标签页ID更新角标，避免异步查询延迟
      this.updateBadgeForTab(activeInfo.tabId)

      // 通知更新
      if (this.onUpdateCallback) {
        this.onUpdateCallback()
      }
    })
  }

  /**
   * 更新角标显示
   */
  private async updateBadge() {
    try {
      const tabs = await chrome.tabs.query({ active: true, currentWindow: true })
      if (tabs.length > 0) {
        this.updateBadgeForTab(tabs[0].id!)
      }
    } catch (error) {
      console.error('更新角标失败:', error)
    }
  }

  /**
   * 为指定标签页更新角标
   */
  private updateBadgeForTab(tabId: number) {
    if (!this.isMonitoringEnabled) {
      chrome.action.setBadgeText({ text: '' })
      return
    }

    const tabRequests = this.filteredRequestsByTab.get(tabId) || []
    const count = tabRequests.length

    if (count > 0) {
      chrome.action.setBadgeText({
        text: count > 99 ? '99+' : count.toString(),
        tabId: tabId
      })
      chrome.action.setBadgeBackgroundColor({ color: '#FF6B6B' })
    } else {
      chrome.action.setBadgeText({ text: '', tabId: tabId })
    }
  }

  /**
   * 设置网络请求监听器
   */
  private setupRequestListeners() {
    // 监听网络请求开始 - 创建请求记录
    chrome.webRequest.onBeforeRequest.addListener(
      (details) => {
        if (!this.isMonitoringEnabled) return

        // 创建请求信息对象
        const requestInfo: RequestInfo = {
          requestId: details.requestId,
          url: details.url,
          method: details.method || 'GET',
          type: details.type,
          timeStamp: details.timeStamp,
          tabId: details.tabId,
          frameId: details.frameId,
          initiator: details.initiator
        }

        // 存储到待处理请求中
        this.pendingRequests.set(details.requestId, requestInfo)
        this.updateRequestStats(details)
      },
      { urls: ["<all_urls>"] },
      ["requestBody"]
    )

    // 监听网络请求发送（包含请求头）- 添加请求头信息
    chrome.webRequest.onBeforeSendHeaders.addListener(
      (details) => {
        if (!this.isMonitoringEnabled) return

        const requestInfo = this.pendingRequests.get(details.requestId)
        if (requestInfo && details.requestHeaders) {
          requestInfo.requestHeaders = details.requestHeaders
        }
      },
      { urls: ["<all_urls>"] },
      ["requestHeaders"]
    )

    // 监听网络请求响应头 - 添加响应信息
    chrome.webRequest.onHeadersReceived.addListener(
      (details) => {
        if (!this.isMonitoringEnabled) return

        const requestInfo = this.pendingRequests.get(details.requestId)
        if (requestInfo) {
          requestInfo.statusCode = details.statusCode
          requestInfo.statusLine = details.statusLine
          requestInfo.responseHeaders = details.responseHeaders
        }
      },
      { urls: ["<all_urls>"] },
      ["responseHeaders"]
    )

    // 监听网络请求完成 - 打印完整信息并清理
    chrome.webRequest.onCompleted.addListener(
      (details) => {
        if (!this.isMonitoringEnabled) return

        const requestInfo = this.pendingRequests.get(details.requestId)
        if (requestInfo) {
          const duration = Date.now() - details.timeStamp

          // 获取页面标题并处理请求
          this.processCompletedRequest(requestInfo, details, duration)

          // 从待处理请求中移除
          this.pendingRequests.delete(details.requestId)
        }
      },
      { urls: ["<all_urls>"] }
    )

    // 监听网络请求错误 - 打印错误信息并清理
    chrome.webRequest.onErrorOccurred.addListener(
      (details) => {
        if (!this.isMonitoringEnabled) return

        const requestInfo = this.pendingRequests.get(details.requestId)
        if (requestInfo) {
          // 打印完整的请求信息（包含错误）
          this.printCompleteRequestInfo(requestInfo, 'failed', details.error)

          // 从待处理请求中移除
          this.pendingRequests.delete(details.requestId)
        }
      },
      { urls: ["<all_urls>"] }
    )
  }

  /**
   * 获取请求统计信息
   */
  getStats(): RequestStats {
    return { ...this.requestStats }
  }



  /**
   * 获取过滤的请求列表（所有标签页合并）
   */
  getFilteredRequests(): FilteredRequestInfo[] {
    return this.getAllFilteredRequests()
  }

  /**
   * 获取指定标签页的过滤请求列表
   */
  getFilteredRequestsByTab(tabId: number): FilteredRequestInfo[] {
    const tabRequests = this.filteredRequestsByTab.get(tabId) || []
    return tabRequests
  }

  /**
   * 设置更新回调函数
   */
  setUpdateCallback(callback: () => void): void {
    this.onUpdateCallback = callback
  }

  /**
   * 检查监控是否启用
   */
  isEnabled(): boolean {
    return this.isMonitoringEnabled
  }





  /**
   * 清空指定标签页的过滤请求列表
   */
  clearFilteredRequestsByTab(tabId: number): void {
    if (this.filteredRequestsByTab.has(tabId)) {
      this.filteredRequestsByTab.delete(tabId)
      console.log(`🗑️ 已清空标签页 ${tabId} 的过滤请求列表`)

      // 清理相关的 Chrome Storage 数据
      this.cleanupStorageForTab(tabId)

      // 更新角标
      this.updateBadge()

      // 通知更新
      if (this.onUpdateCallback) {
        this.onUpdateCallback()
      }
    } else {
      console.log(`⚠️ 标签页 ${tabId} 没有过滤请求数据`)
    }
  }

  /**
   * 清理指定标签页相关的 Chrome Storage 数据
   */
  private cleanupStorageForTab(tabId: number): void {
    // 异步执行清理，不阻塞主流程
    setTimeout(() => {
      chrome.storage.local.get(null, (allData) => {
        if (chrome.runtime.lastError) {
          console.error(`获取标签页 ${tabId} 存储数据失败:`, chrome.runtime.lastError)
          return
        }

        const keysToRemove: string[] = []

        // 查找与该标签页相关的存储键
        for (const key of Object.keys(allData || {})) {
          // 清理下载数据和下载状态（requestId 通常包含 tabId）
          if ((key.startsWith('downloadData_') || key.startsWith('downloadStatus_')) &&
            key.includes(`${tabId}-`)) {
            keysToRemove.push(key)
          }
        }

        // 批量删除
        if (keysToRemove.length > 0) {
          chrome.storage.local.remove(keysToRemove, () => {
            if (chrome.runtime.lastError) {
              console.error(`删除标签页 ${tabId} 存储数据失败:`, chrome.runtime.lastError)
            } else {
              console.log(`已清理标签页 ${tabId} 的 ${keysToRemove.length} 个存储条目`)
            }
          })
        }
      })
    }, 0)
  }


}
