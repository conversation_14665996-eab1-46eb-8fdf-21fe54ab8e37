# SnapAny Extension 下载流程图

## 概述

本文档详细描述了 SnapAny 浏览器扩展的完整下载流程，从用户触发下载到文件保存完成的全过程。

## 系统架构

下载系统由以下核心组件组成：

- **UI层**：popup.tsx / sidepanel.tsx - 用户界面
- **Background Script**：后台脚本，包含 DownloadManager 和 HeaderManager
- **Content Script Bridge**：downloader-bridge.ts - 扩展与外部页面的通信桥梁
- **外部下载页面**：http://localhost:3000/downloader - 实际执行下载

## 完整下载流程图

```mermaid
flowchart TD
    %% 用户界面层
    A[用户在 Popup/Sidepanel 选择媒体] --> B[点击下载按钮]
    B --> C[VideoItem.handleDownload 函数]

    %% 数据准备和存储
    C --> D[准备下载数据对象]
    D --> E[存储到 Chrome Storage]
    E --> F[存储数据到 chrome.storage.local]
    F --> G[window.open 打开外部下载器页面]

    %% 外部下载页面初始化
    G --> H[外部下载页面加载 localhost:3456]
    H --> I[Content Script Bridge 注入]
    I --> J[从 URL 获取 requestId]
    J --> K[发送 GET_DOWNLOAD_DATA_BY_ID]

    %% 数据获取
    K --> L[Background 返回 DownloadData]
    L --> M[页面显示下载信息]
    
    %% 下载触发
    M --> N[页面初始化完成]
    N --> O[获取URL参数中的requestId]
    O --> P[向插件请求下载数据]
    P --> Q[收到数据后自动开始下载]
    Q --> R{检测文件类型}
    
    %% 标准文件下载路径
    R -->|标准媒体文件| S[调用 downloadFileWithHeaders]
    S --> T[发送 DOWNLOAD_FILE_WITH_HEADERS 消息]
    T --> U[DownloadManager.handleDownloadFileWithHeaders]
    U --> V[HeaderManager 设置 declarativeNetRequest 规则]
    V --> W[注入请求头到浏览器网络层]
    W --> X[发送 START_DOWNLOAD_IN_WEBPAGE 消息]
    X --> Y[调用 downloadInWebpage 函数]
    
    %% M3U8 下载路径
    R -->|M3U8 流媒体| Z[调用 downloadM3u8File]
    Z --> AA[获取 M3U8 播放列表]
    AA --> BB{检测播放列表类型}
    BB -->|主播放列表| CC[获取子播放列表]
    BB -->|媒体播放列表| DD[解析分片 URL]
    CC --> DD
    DD --> EE[并发下载所有分片]
    EE --> FF[合并分片为 Blob]
    
    %% 标准下载执行
    Y --> GG[使用 fetch 发起请求]
    GG --> HH[获取 Response Stream]
    HH --> II[逐块读取数据]
    II --> JJ[更新进度条]
    JJ --> KK{是否读取完成?}
    KK -->|否| II
    KK -->|是| LL[合并数据块为 Uint8Array]
    LL --> MM[创建 Blob 对象]
    
    %% 文件保存
    FF --> NN[保存 M3U8 文件]
    MM --> OO[保存标准文件]
    NN --> PP[使用 a 标签 download 属性]
    OO --> PP
    PP --> QQ[文件下载到本地]
    
    %% 完成处理
    QQ --> RR[更新 UI 状态为已保存]
    RR --> SS[显示重新保存按钮]
    PP --> TT[发送 DOWNLOAD_COMPLETED 消息]
    TT --> UU[Background 清理资源]
    UU --> VV[清理 declarativeNetRequest 规则]
    VV --> WW[下载流程完成]
    
    %% 错误处理
    GG -.->|网络错误| XX[显示错误信息]
    AA -.->|解析错误| XX
    EE -.->|下载失败| XX
    PP -.->|保存失败| XX
    XX --> YY[支持重试下载]
    
    %% 样式定义
    classDef uiClass fill:#e1f5fe
    classDef backgroundClass fill:#f3e5f5
    classDef pageClass fill:#e8f5e8
    classDef processClass fill:#fff3e0
    classDef errorClass fill:#ffebee
    
    class A,B,RR,SS uiClass
    class C,D,E,F,K,L,T,U,V,W,TT,UU,VV backgroundClass
    class G,H,I,J,M,N,O,P,Q,S,X,Y,Z,AA,BB,CC,DD,EE,FF,GG,HH,II,JJ,LL,MM,NN,OO,PP,QQ pageClass
    class R,BB,KK processClass
    class XX,YY errorClass
```

## 关键设计特点

### 1. 分离架构
- **数据存储**：插件负责数据存储到 Chrome Storage
- **页面打开**：使用 `window.open` 打开外部下载器页面
- **执行分离**：外部页面 (localhost:3456) 负责实际下载执行
- **通信桥梁**：Content Script Bridge 处理跨环境通信

### 2. 请求头注入机制
- 使用 Chrome `declarativeNetRequest` API
- 在浏览器网络层注入请求头
- 避免 CORS 问题，支持携带认证信息

### 3. 双重下载策略
- **标准文件**：通过 Background 设置请求头，页面端执行下载
- **M3U8 流媒体**：页面端直接处理，支持分片下载和合并

### 4. 进度监控
- 实时进度更新（每 0.5 秒）
- 显示下载百分比、速度、文件大小
- 支持暂停/继续功能

### 5. 资源管理
- 及时清理 declarativeNetRequest 规则
- Object URL 及时释放避免内存泄漏
- 按标签页跟踪和清理下载任务

## 消息流序列

```mermaid
sequenceDiagram
    participant UI as UI Layer
    participant BG as Background Script
    participant ST as Storage
    participant TAB as Download Page
    participant BR as Browser Network
    
    UI->>BG: CREATE_DOWNLOADER
    BG->>ST: 存储 DownloadData
    BG->>TAB: 创建下载页面
    TAB->>BG: GET_DOWNLOAD_DATA_BY_ID
    BG->>TAB: 返回 DownloadData
    TAB->>BG: DOWNLOAD_FILE_WITH_HEADERS
    BG->>BR: 设置 declarativeNetRequest 规则
    BG->>TAB: START_DOWNLOAD_IN_WEBPAGE
    TAB->>BR: fetch 请求（自动携带请求头）
    BR->>TAB: 返回数据流
    TAB->>TAB: 保存文件
    TAB->>BG: DOWNLOAD_COMPLETED
    BG->>BR: 清理规则
```

## 错误处理机制

1. **网络错误**：显示具体错误信息，支持重试
2. **解析错误**：M3U8 解析失败时的降级处理
3. **权限错误**：请求头设置失败的处理
4. **存储错误**：文件保存失败的处理
5. **资源清理**：确保异常情况下的资源释放

## 性能优化

1. **流式下载**：避免大文件占用过多内存
2. **并发控制**：M3U8 分片下载的并发限制
3. **进度节流**：避免过频繁的 UI 更新
4. **资源复用**：复用 declarativeNetRequest 规则
5. **内存管理**：及时释放 Blob 和 Object URL

---

*此流程图基于 SnapAny Extension 的实际代码实现，详细展示了从用户操作到文件保存的完整下载流程。*
