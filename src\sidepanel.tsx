import { useState, useEffect, useCallback } from "react"
import { useTranslation } from "react-i18next"

import "@/style.css"
import "@/i18n"
import { VideoList, BottomToolbar, CustomScrollArea } from "@/components"
import type { VideoData } from "@/types"
import {
  getFilteredRequests,
  getCurrentTabId,
  convertRequestsToVideoData,
  listenToBackgroundUpdates,
  connectToBackgroundAsSidepanel,
  clearFilteredRequestsByTab
} from "@/utils"

function SidePanel() {
  const { t } = useTranslation()
  const [videoList, setVideoList] = useState<VideoData[]>([])
  const [currentTabId, setCurrentTabId] = useState<number | undefined>(undefined)

  // 获取视频数据
  const loadVideoData = useCallback(async () => {
    try {
      // 获取当前标签页ID
      const tabId = await getCurrentTabId()
      setCurrentTabId(tabId)

      // 获取过滤的请求列表
      const filteredRequests = await getFilteredRequests(tabId)

      // 转换为VideoData格式
      const videoData = convertRequestsToVideoData(filteredRequests)

      setVideoList(videoData)
      console.log("✅ SidePanel加载到视频数据:", videoData.length, "条")
    } catch (error) {
      console.error("❌ SidePanel加载视频数据失败:", error)
      setVideoList([])
    }
  }, [])

  // 组件挂载时设置连接、监听和加载数据
  useEffect(() => {
    // 建立与background的连接
    const port = connectToBackgroundAsSidepanel()

    // 初始加载数据
    loadVideoData()

    // 监听background更新
    const cleanup = listenToBackgroundUpdates(() => {
      console.log("🔄 SidePanel收到background更新通知，重新加载数据")
      loadVideoData()
    })

    // 设置定期刷新（sidepanel通常保持打开，需要定期更新）
    const interval = setInterval(() => {
      loadVideoData()
    }, 15000) // 每15秒刷新一次

    return () => {
      cleanup()
      clearInterval(interval)
      port?.disconnect()
    }
  }, [loadVideoData])

  // 清空当前标签页的嗅探记录
  const handleClearCurrentTab = useCallback(async () => {
    try {
      const tabId = await getCurrentTabId()
      if (tabId) {
        const success = await clearFilteredRequestsByTab(tabId)
        if (success) {
          console.log("✅ SidePanel已清空当前标签页的嗅探记录")
          // 清空后重新加载数据
          setVideoList([])
        } else {
          console.error("❌ SidePanel清空当前标签页的嗅探记录失败")
        }
      }
    } catch (error) {
      console.error("❌ SidePanel清空当前标签页的嗅探记录异常:", error)
    }
  }, [])

  return (
    <div className="flex flex-col w-full h-screen bg-gray-100 overflow-hidden">
      {/* 主内容区域 - 使用自定义滚动区域 */}
      <div className="flex-1 min-h-0">
        {videoList.length === 0 ? (
          // 空状态
          <div className="flex flex-col justify-center items-center p-2 gap-2.5 h-full">
            <div className="text-sm font-normal text-gray-500 text-center leading-relaxed">
              {t('popup.noMediaFound')}<br />
              {t('popup.playVideoToDetect')}
            </div>
          </div>
        ) : (
          // 有视频时显示列表 - 使用自定义滚动条
          <CustomScrollArea className="h-full">
            <div className="p-2 gap-2.5 pb-4">
              <VideoList videos={videoList} tabId={currentTabId} />
            </div>
          </CustomScrollArea>
        )}
      </div>

      {/* 底部工具栏 - 固定在底部，不使用absolute定位 */}
      <div className="flex-shrink-0">
        <BottomToolbar variant="sidepanel" onClearCurrentTab={handleClearCurrentTab} />
      </div>
    </div>
  )
}

export default SidePanel
