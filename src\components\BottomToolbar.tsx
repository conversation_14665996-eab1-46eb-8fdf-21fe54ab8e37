/**
 * 底部工具栏组件
 * 提供视图模式切换和清空功能
 */

import { useTranslation } from "react-i18next"
import { Tooltip } from "flowbite-react"
import type { BottomToolbarProps } from "@/types"
import { toggleSidepanel, setCurrentViewMode } from "@/utils"
import { IconLayoutSidebarRightCollapse, IconLayoutNavbarCollapse, IconTrash } from "@tabler/icons-react"

// 自定义Tooltip主题
const customTooltipTheme = {
  target: "w-fit",
  animation: "transition-opacity",
  arrow: {
    base: "absolute z-10 h-2 w-2 rotate-45",
    style: {
      dark: "bg-gray-900 dark:bg-gray-700",
      light: "bg-white",
      auto: "bg-white dark:bg-gray-700"
    },
    placement: "-4px"
  },
  base: "absolute z-10 inline-block rounded-lg px-3 py-2 text-sm font-medium shadow-sm",
  hidden: "invisible opacity-0",
  style: {
    dark: "bg-gray-900 text-white dark:bg-gray-700",
    light: "border border-gray-200 bg-white text-gray-900",
    auto: "border border-gray-200 bg-white text-gray-900 dark:border-none dark:bg-gray-700 dark:text-white"
  },
  content: "relative z-20"
}

export default function BottomToolbar({ variant = "popup", onClearCurrentTab }: BottomToolbarProps) {
  const { t } = useTranslation()

  /**
   * 处理视图模式切换
   * 支持在popup和sidepanel之间切换，并自动管理窗口状态
   */
  const handleViewModeToggle = async () => {
    try {
      if (variant === "popup") {
        // === 从popup切换到sidepanel ===
        console.log("切换到侧边栏模式")

        // 保存新的视图模式到本地存储
        await setCurrentViewMode("sidepanel")

        // 配置侧边栏选项和行为
        const success = await toggleSidepanel()

        if (success) {
          // 在用户手势响应中直接打开侧边栏
          try {
            const currentWindow = await chrome.windows.getCurrent()
            await chrome.sidePanel.open({ windowId: currentWindow.id })
            console.log("侧边栏已自动打开")
          } catch (openError) {
            console.warn("自动打开侧边栏失败:", openError)
          }

          // 关闭当前popup窗口
          window.close()
        } else {
          console.error("准备侧边栏失败")
          // 失败时回滚到原来的模式
          await setCurrentViewMode("popup")
        }
      } else {
        // === 从sidepanel切换到popup ===
        console.log("切换到弹窗模式")

        // 保存新的视图模式到本地存储
        await setCurrentViewMode("popup")

        // 禁用侧边栏的自动打开行为
        try {
          await chrome.sidePanel.setPanelBehavior({ openPanelOnActionClick: false })
        } catch (error) {
          console.warn("无法设置侧边栏行为:", error)
        }

        // 关闭当前侧边栏窗口
        window.close()

        // 注意: popup不能自动弹出，用户需要手动点击扩展图标
        console.log("已切换到弹窗模式，请点击扩展图标打开弹窗")
      }
    } catch (error) {
      console.error("切换视图模式失败:", error)
    }
  }

  return (
    <div className={`flex justify-between items-center w-full h-10 bg-white`}>
      {/* 视图模式切换按钮 - 根据当前模式显示不同图标 */}
      <Tooltip
        content={variant === "popup" ? t('toolbar.switchToSidepanel') : t('toolbar.switchToPopup')}
        style="dark"
        placement="top"
        arrow={true}
        theme={customTooltipTheme}
      >
        <button
          type="button"
          className="flex justify-center items-center w-10 h-10 rounded-full hover:bg-gray-100"
          onClick={handleViewModeToggle}
        >
          {variant === "popup" ? (
            // Popup模式图标 - 表示切换到侧边栏
            <IconLayoutSidebarRightCollapse className="w-5 h-5 text-gray-500" />
          ) : (
            // Sidepanel模式图标 - 表示切换到弹窗
            <IconLayoutNavbarCollapse className="w-5 h-5 text-gray-500" />
          )}
        </button>
      </Tooltip>

      {/* 清空按钮 - 清空当前标签页的嗅探记录 */}
      <Tooltip
        content={t('toolbar.clearCurrentTab')}
        style="dark"
        placement="top"
        arrow={true}
        theme={customTooltipTheme}
      >
        <button
          type="button"
          className="flex flex-row justify-center items-center px-2.5 py-0.5 w-10 h-10 rounded-full hover:bg-gray-100"
          onClick={onClearCurrentTab}
        >
          <IconTrash className="w-5 h-5 text-gray-500" />
        </button>
      </Tooltip>
    </div>
  )
}