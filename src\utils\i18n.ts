import { getBackgroundText } from '../i18n'

/**
 * 在background script中使用的多语言工具函数
 * @param key 翻译键，支持嵌套键如 'common.title'
 * @returns 翻译后的文本
 */
export function t(key: string): string {
  return getBackgroundText(key)
}

/**
 * 获取当前语言代码
 * @returns 语言代码，如 'en', 'zh_CN'
 */
export function getCurrentLanguage(): string {
  try {
    const chromeLanguage = chrome?.i18n?.getUILanguage?.()
    if (chromeLanguage) {
      const normalizedLang = chromeLanguage.toLowerCase().replace('-', '_')
      if (normalizedLang.startsWith('zh')) {
        // 根据地区代码决定使用简体还是繁体中文
        if (normalizedLang.includes('tw') || normalizedLang.includes('hk') || normalizedLang.includes('mo') || normalizedLang.includes('hant')) {
          return 'zh_TW'
        }
        return 'zh_CN'
      }
      return normalizedLang.split('_')[0] || 'en'
    }
  } catch (error) {
    console.log('Chrome i18n API不可用，使用navigator.language')
  }

  const browserLanguage = navigator.language || 'en'
  const normalizedBrowserLang = browserLanguage.toLowerCase().replace('-', '_')

  if (normalizedBrowserLang.startsWith('zh')) {
    // 根据地区代码决定使用简体还是繁体中文
    if (normalizedBrowserLang.includes('tw') || normalizedBrowserLang.includes('hk') || normalizedBrowserLang.includes('mo') || normalizedBrowserLang.includes('hant')) {
      return 'zh_TW'
    }
    return 'zh_CN'
  }

  return normalizedBrowserLang.split('_')[0] || 'en'
}

/**
 * 检查是否为中文环境
 * @returns 是否为中文
 */
export function isChinese(): boolean {
  const lang = getCurrentLanguage()
  return lang === 'zh_CN' || lang === 'zh_TW'
}

/**
 * 检查是否为简体中文环境
 * @returns 是否为简体中文
 */
export function isSimplifiedChinese(): boolean {
  return getCurrentLanguage() === 'zh_CN'
}

/**
 * 检查是否为繁体中文环境
 * @returns 是否为繁体中文
 */
export function isTraditionalChinese(): boolean {
  return getCurrentLanguage() === 'zh_TW'
}
